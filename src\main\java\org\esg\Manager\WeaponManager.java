package org.esg.Manager;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.esg.models.Weapon;
import org.esg.utils.WeaponUtils;
import org.esg.weapons.WeaponFactory;
import java.util.logging.Logger;

public final class WeaponManager {
    private static final Logger LOGGER = Logger.getLogger(WeaponManager.class.getName());

    private WeaponManager() {}

    public static void giveWeapon(Player player, Weapon weapon) {
        if (player == null || weapon == null) {
            LOGGER.severe("giveWeapon: Invalid parameters - player or weapon is null");
            return;
        }

        ItemStack baseItem = WeaponFactory.toItemStack(weapon);
        if (baseItem == null) {
            LOGGER.severe("Failed to create ItemStack for " + weapon.getName());
            return;
        }

        ItemStack item = WeaponUtils.applyWeaponToItem(baseItem, weapon, player);
        if (item == null) {
            LOGGER.severe("Failed to apply NBT for " + weapon.getName());
            return;
        }

        player.getInventory().addItem(item);
    }

    public static Weapon getActiveWeapon(Player player) {
        ItemStack itemInHand = player.getItemInHand();
        return itemInHand != null ? WeaponUtils.getWeaponFromItem(itemInHand, player) : null;
    }
}