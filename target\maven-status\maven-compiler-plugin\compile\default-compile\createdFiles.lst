org\esg\systems\WallClimbSystem.class
org\esg\utils\StatsUtil.class
org\esg\models\ArmorFactory$1.class
org\esg\listeners\ArmorListener.class
org\esg\utils\MessageHandler$Messages.class
org\esg\Manager\KnockbackImmunityManager$2.class
org\esg\utils\NBTUtils$CachedFiringState.class
org\esg\utils\WeaponUtils.class
org\esg\listeners\ReloadListener.class
org\esg\utils\AnimationBlocker$1.class
org\esg\systems\VisualEffectsSystem.class
org\esg\utils\ImpactSoundManager.class
org\esg\listeners\WeaponStatsListener.class
org\esg\listeners\StatsListener$DamageInfo.class
org\esg\utils\StatsUtil$DamageInfo.class
org\esg\weapons\RPG$1.class
org\esg\models\Weapon$3.class
org\esg\utils\ActionBarUtil.class
org\esg\enums\AmmoType.class
org\esg\commands\StatsCommand.class
org\esg\systems\VisualEffectsSystem$1.class
org\esg\commands\ArmorCommand.class
org\esg\enums\WeaponType.class
org\esg\enums\ArmorType.class
org\esg\systems\PenetrationSystem.class
org\esg\listeners\PlayerJoinListener.class
org\esg\api\WeaponAPI.class
org\esg\weapons\Oitao.class
org\esg\Manager\ArmorManager.class
org\esg\utils\KnockbackUtil$KnockbackModifiers.class
org\esg\systems\PenetrationSystem$1.class
org\esg\utils\DamageIndicatorManager.class
org\esg\weapons\AK47.class
org\esg\listeners\WeaponSwitchListener.class
org\esg\listeners\WeaponMenuListener.class
org\esg\models\Weapon$6.class
org\esg\weapons\RPG.class
org\esg\listeners\SniperScopeListener$1.class
org\esg\weapons\LancaChamas$4.class
org\esg\weapons\LancaChamas.class
org\esg\weapons\WeaponFactory.class
org\esg\utils\AnimationBlocker.class
org\esg\utils\ItemAnimationUtils.class
org\esg\systems\HeadshotSystem$1.class
org\esg\models\BurstFireWeapon.class
org\esg\Manager\PvPTagManager.class
org\esg\stats\PlayerStats.class
org\esg\weapons\LancaChamas$3.class
org\esg\enums\ArmorPiece.class
org\esg\utils\NBTUtils.class
org\esg\api\ArmorAPI.class
org\esg\Manager\ArmorManager$1.class
org\esg\models\Armor.class
org\esg\models\Weapon$5.class
org\esg\models\BurstFireWeapon$1.class
org\esg\models\Weapon$4.class
org\esg\ui\ArmorMenu.class
org\esg\systems\HeadshotSystem.class
org\esg\commands\WeaponCommand.class
org\esg\weapons\LancaChamas$2.class
org\esg\models\Weapon$1.class
org\esg\listeners\ShootListener$1.class
org\esg\ui\EnhancedHUD$1.class
org\esg\listeners\SniperScopeListener.class
org\esg\Main$1.class
org\esg\Manager\WeaponManager.class
org\esg\Manager\KnockbackImmunityManager$1.class
org\esg\weapons\UZI.class
org\esg\listeners\PvPTagListener.class
org\esg\ui\EnhancedHUD.class
org\esg\models\Weapon$CachedReloadingState.class
org\esg\Manager\KnockbackImmunityManager.class
org\esg\listeners\ShootListener.class
org\esg\Main.class
org\esg\utils\PvPTagUtil.class
org\esg\weapons\AR15.class
org\esg\Effects\SoundEffects.class
org\esg\listeners\StatsListener.class
org\esg\weapons\DMR.class
org\esg\models\Armor$1.class
org\esg\utils\KnockbackUtil.class
org\esg\listeners\ScoreboardListener.class
org\esg\weapons\LancaChamas$1.class
org\esg\models\Weapon.class
org\esg\weapons\Shotgun.class
org\esg\systems\WallClimbSystem$1.class
org\esg\utils\AmmoCache.class
org\esg\ui\WeaponMenu$1.class
org\esg\Manager\PvPTagManager$1.class
org\esg\weapons\Barrett.class
org\esg\listeners\AmmoSyncListener.class
org\esg\listeners\HungerListener.class
org\esg\listeners\WeaponHeldListener.class
org\esg\Manager\ScoreboardManager.class
org\esg\models\ArmorFactory.class
org\esg\Effects\ParticleCat.class
org\esg\stats\PlayerStats$1.class
org\esg\enums\ArmorPiece$1.class
org\esg\Manager\InvulnerabilityManager.class
org\esg\stats\StatsManager.class
org\esg\weapons\M4A1.class
org\esg\api\WeaponAPI$1.class
org\esg\listeners\SniperScopeListener$SniperScopeListenerHolder.class
org\esg\utils\MessageHandler.class
org\esg\Main$2.class
org\esg\models\Weapon$2.class
org\esg\ui\WeaponMenu.class
org\esg\commands\KnockbackCommand.class
org\esg\weapons\Dragunov.class
