package org.esg.commands;

import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.esg.Manager.KnockbackImmunityManager;

/**
 * Command to manage and debug the knockback immunity system.
 * Usage: /knockback [status|reload|enable|disable|check <player>]
 */
public class KnockbackCommand implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("esg.knockback.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }

        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "status":
                handleStatus(sender);
                break;
            case "reload":
                handleReload(sender);
                break;
            case "enable":
                handleEnable(sender);
                break;
            case "disable":
                handleDisable(sender);
                break;
            case "check":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "Usage: /knockback check <player>");
                    return true;
                }
                handleCheck(sender, args[1]);
                break;
            case "test":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "Usage: /knockback test <player>");
                    return true;
                }
                handleTest(sender, args[1]);
                break;
            default:
                sendHelp(sender);
                break;
        }

        return true;
    }

    private void sendHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Knockback Immunity Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/knockback status" + ChatColor.WHITE + " - Show system status");
        sender.sendMessage(ChatColor.YELLOW + "/knockback reload" + ChatColor.WHITE + " - Reload configuration");
        sender.sendMessage(ChatColor.YELLOW + "/knockback enable" + ChatColor.WHITE + " - Enable the system");
        sender.sendMessage(ChatColor.YELLOW + "/knockback disable" + ChatColor.WHITE + " - Disable the system");
        sender.sendMessage(ChatColor.YELLOW + "/knockback check <player>" + ChatColor.WHITE + " - Check player immunity");
        sender.sendMessage(ChatColor.YELLOW + "/knockback test <player>" + ChatColor.WHITE + " - Test immunity system");
    }

    private void handleStatus(CommandSender sender) {
        boolean enabled = KnockbackImmunityManager.isSystemEnabled();
        int activeCount = KnockbackImmunityManager.getActiveImmunityCount();

        sender.sendMessage(ChatColor.GOLD + "=== Knockback Immunity Status ===");
        sender.sendMessage(ChatColor.YELLOW + "System Enabled: " +
                          (enabled ? ChatColor.GREEN + "YES" : ChatColor.RED + "NO"));
        sender.sendMessage(ChatColor.YELLOW + "Active Immunities: " + ChatColor.WHITE + activeCount);
    }

    private void handleReload(CommandSender sender) {
        try {
            KnockbackImmunityManager.reloadConfiguration();
            sender.sendMessage(ChatColor.GREEN + "Knockback immunity configuration reloaded successfully!");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Error reloading configuration: " + e.getMessage());
        }
    }

    private void handleEnable(CommandSender sender) {
        KnockbackImmunityManager.setSystemEnabled(true);
        sender.sendMessage(ChatColor.GREEN + "Knockback immunity system enabled!");
    }

    private void handleDisable(CommandSender sender) {
        KnockbackImmunityManager.setSystemEnabled(false);
        sender.sendMessage(ChatColor.YELLOW + "Knockback immunity system disabled!");
    }

    private void handleCheck(CommandSender sender, String playerName) {
        Player target = sender.getServer().getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player '" + playerName + "' not found.");
            return;
        }

        boolean immune = KnockbackImmunityManager.isImmune(target);
        long remainingTime = KnockbackImmunityManager.getRemainingImmunityTime(target);

        sender.sendMessage(ChatColor.GOLD + "=== " + target.getName() + " Immunity Status ===");
        sender.sendMessage(ChatColor.YELLOW + "Currently Immune: " +
                          (immune ? ChatColor.RED + "YES" : ChatColor.GREEN + "NO"));

        if (immune && remainingTime > 0) {
            sender.sendMessage(ChatColor.YELLOW + "Remaining Time: " + ChatColor.WHITE +
                              remainingTime + "ms (" + String.format("%.1f", remainingTime / 1000.0) + "s)");
        }
    }

    private void handleTest(CommandSender sender, String playerName) {
        Player target = sender.getServer().getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player '" + playerName + "' not found.");
            return;
        }

        sender.sendMessage(ChatColor.GOLD + "=== Testing Knockback Immunity for " + target.getName() + " ===");

        // Test 1: Check if system is enabled
        boolean systemEnabled = KnockbackImmunityManager.isSystemEnabled();
        sender.sendMessage(ChatColor.YELLOW + "System Enabled: " +
                          (systemEnabled ? ChatColor.GREEN + "YES" : ChatColor.RED + "NO"));

        // Test 2: Check current immunity status
        boolean currentlyImmune = KnockbackImmunityManager.isImmune(target);
        sender.sendMessage(ChatColor.YELLOW + "Currently Immune: " +
                          (currentlyImmune ? ChatColor.RED + "YES" : ChatColor.GREEN + "NO"));

        // Test 3: Apply test immunity
        sender.sendMessage(ChatColor.YELLOW + "Applying test immunity (AK-47, RIFLE)...");
        KnockbackImmunityManager.applyImmunity(target, "AK-47", org.esg.enums.WeaponType.RIFLE);

        // Test 4: Check immunity after application
        boolean immuneAfterTest = KnockbackImmunityManager.isImmune(target);
        long remainingTime = KnockbackImmunityManager.getRemainingImmunityTime(target);

        sender.sendMessage(ChatColor.YELLOW + "Immune After Test: " +
                          (immuneAfterTest ? ChatColor.GREEN + "YES" : ChatColor.RED + "NO"));

        if (immuneAfterTest && remainingTime > 0) {
            sender.sendMessage(ChatColor.YELLOW + "Remaining Time: " + ChatColor.WHITE +
                              remainingTime + "ms (" + String.format("%.1f", remainingTime / 1000.0) + "s)");
        }

        sender.sendMessage(ChatColor.GREEN + "Test completed! Check console for detailed logs.");
    }
}
