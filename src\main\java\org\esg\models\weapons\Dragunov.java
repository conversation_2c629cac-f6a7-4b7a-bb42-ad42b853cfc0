package org.esg.models.weapons;

import org.bukkit.entity.Player;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;
import org.esg.models.Weapon;

/**
 * Dragunov - Uma sniper semi-automática
 *
 * Características:
 * - Alta taxa de disparo para uma sniper
 * - <PERSON><PERSON> dano que snipers convencionais
 * - <PERSON><PERSON><PERSON> mais rápida
 * - <PERSON><PERSON> precis<PERSON>
 */
public class <PERSON><PERSON><PERSON> extends Weapon {

    public Dragunov() {
        super(
            "<PERSON><PERSON><PERSON>",         // nome
            WeaponType.SNIPER,  // tipo (mantido como SNIPER para aplicar os efeitos de sniper)
            AmmoType._762MM,    // tipo de munição (7.62mm para sniper)
            14.0,               // dano (menor que snipers normais)
            80.0,               // alcance em blocos
            0.85,               // precisão (0.0 a 1.0)
            2.5,                // taxa de disparo (tiros por segundo - maior que snipers normais)
            180.0,              // velocidade do projétil
            10,                 // capacidade máxima
            10,                 // munição inicial
            3,                  // tempo de recarga (segundos)
            1,                  // número de projéteis por tiro
            2.0                 // multiplicador de headshot
        );
    }
}