C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\UZI.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\Barrett.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\enums\ArmorPiece.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\systems\PenetrationSystem.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Effects\SoundEffects.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\ShootListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\systems\HeadshotSystem.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\models\Armor.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\models\weapons\TresOitao.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Manager\KnockbackImmunityManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\ReloadListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\KnockbackUtil.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\AnimationBlocker.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\MessageHandler.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\ItemAnimationUtils.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\WeaponFactory.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\commands\ArmorCommand.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Effects\ParticleCat.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\models\Weapon.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\LancaChamas.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\commands\KnockbackCommand.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\ui\ArmorMenu.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\enums\AmmoType.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\ui\WeaponMenu.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\ImpactSoundManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\StatsListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\WeaponHeldListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\WeaponUtils.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\Dragunov.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\PvPTagListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\RPG.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\models\weapons\LancaChamas.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Manager\ScoreboardManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\ArmorListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\SniperScopeListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Manager\InvulnerabilityManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\systems\WallClimbSystem.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\Shotgun.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\ScoreboardListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\enums\ArmorType.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\api\ArmorAPI.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\models\weapons\Dragunov.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\M4A1.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\stats\StatsManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Manager\WeaponManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\StatsUtil.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Manager\ArmorManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\AmmoCache.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\stats\PlayerStats.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Manager\PvPTagManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\models\BurstFireWeapon.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\DMR.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\PvPTagUtil.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\AK47.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\WeaponMenuListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\NBTUtils.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\WeaponStatsListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\Main.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\PlayerJoinListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\WeaponSwitchListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\models\ArmorFactory.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\Oitao.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\commands\StatsCommand.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\ui\EnhancedHUD.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\commands\WeaponCommand.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\enums\WeaponType.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\ActionBarUtil.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\HungerListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\listeners\AmmoSyncListener.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\weapons\AR15.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\utils\DamageIndicatorManager.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\systems\VisualEffectsSystem.java
C:\Users\<USER>\Desktop\esgotoserver\src\main\java\org\esg\api\WeaponAPI.java
